<template>
	<view class="main">
		<!-- 页面标题 -->
		<view class="header">
			<text class="title">语音识别结果编辑</text>
		</view>

		<!-- 语音识别结果列表 -->
		<view class="c-content">
			<view class="utterance-list">
				<view v-for="(utterance, index) in utterances" :key="index" class="utterance-item">
					<view class="utterance-content">
						<text class="utterance-text">{{ utterance.text }}</text>
						<button class="edit-btn" @click="openEditModal(index)">
							修改
						</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部操作按钮 -->
		<view class="footer">
			<view class="footer-buttons">
				<button class="reset-btn" @click="resetData">重置</button>
				<button class="next-btn" @click="handleNext">下一步</button>
			</view>
		</view>

		<!-- 编辑弹窗 -->
		<view v-if="showEditModal" class="modal-overlay" @click="closeEditModal">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">编辑文字</text>
					<text class="close-btn" @click="closeEditModal">×</text>
				</view>

				<view class="modal-body">
					<view class="words-grid">
						<view v-for="(word, wordIndex) in currentEditWords" :key="wordIndex" class="word-item">
							<input class="word-input" :value="word.text" @input="updateWord(wordIndex, $event)"
								maxlength="1" type="text" />
						</view>
					</view>
				</view>

				<view class="modal-footer">
					<button class="confirm-btn" @click="confirmEdit">确认修改</button>
					<button class="cancel-btn" @click="closeEditModal">取消</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 示例数据 - 实际使用时从服务端获取
				utterances: [{
						"text": "苏州的家人们看过来",
						"start_time": 2040,
						"end_time": 4220,
						"words": [{
								"text": "苏",
								"start_time": 2040,
								"end_time": 2200,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "州",
								"start_time": 2200,
								"end_time": 2360,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "的",
								"start_time": 2360,
								"end_time": 2560,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "家",
								"start_time": 2640,
								"end_time": 2880,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "人",
								"start_time": 2880,
								"end_time": 3000,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "们",
								"start_time": 3000,
								"end_time": 3200,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "看",
								"start_time": 3280,
								"end_time": 3520,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "过",
								"start_time": 3520,
								"end_time": 3720,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "来",
								"start_time": 3720,
								"end_time": 4220,
								"attribute": {
									"event": "speech"
								}
							}
						]
					},
					{
						"text": "我们扎根在一个相城区",
						"start_time": 4320,
						"end_time": 7040,
						"words": [{
								"text": "我",
								"start_time": 4320,
								"end_time": 4680,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "们",
								"start_time": 4680,
								"end_time": 4960,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "扎",
								"start_time": 5000,
								"end_time": 5240,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "根",
								"start_time": 5280,
								"end_time": 5480,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "在",
								"start_time": 5480,
								"end_time": 5720,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "一",
								"start_time": 5960,
								"end_time": 6160,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "个",
								"start_time": 6160,
								"end_time": 6400,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "相",
								"start_time": 6400,
								"end_time": 6640,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "城",
								"start_time": 6640,
								"end_time": 6840,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "区",
								"start_time": 6840,
								"end_time": 7040,
								"attribute": {
									"event": "speech"
								}
							}
						]
					},
					{
						"text": "八千平米的呃",
						"start_time": 7040,
						"end_time": 8920,
						"words": [{
								"text": "八",
								"start_time": 7040,
								"end_time": 7240,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "千",
								"start_time": 7240,
								"end_time": 7480,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "平",
								"start_time": 7480,
								"end_time": 7720,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "米",
								"start_time": 7720,
								"end_time": 7840,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "的",
								"start_time": 7840,
								"end_time": 8080,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "呃",
								"start_time": 8760,
								"end_time": 8920,
								"attribute": {
									"event": "speech"
								}
							}
						]
					},
					{
						"text": "工厂直营店",
						"start_time": 8920,
						"end_time": 10220,
						"words": [{
								"text": "工",
								"start_time": 8920,
								"end_time": 9080,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "厂",
								"start_time": 9080,
								"end_time": 9320,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "直",
								"start_time": 9320,
								"end_time": 9520,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "营",
								"start_time": 9520,
								"end_time": 9680,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "店",
								"start_time": 9720,
								"end_time": 10220,
								"attribute": {
									"event": "speech"
								}
							}
						]
					},
					{
						"text": "位置偏了点",
						"start_time": 11360,
						"end_time": 12700,
						"words": [{
								"text": "位",
								"start_time": 11360,
								"end_time": 11600,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "置",
								"start_time": 11600,
								"end_time": 11800,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "偏",
								"start_time": 11840,
								"end_time": 12040,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "了",
								"start_time": 12040,
								"end_time": 12200,
								"attribute": {
									"event": "speech"
								}
							},
							{
								"text": "点",
								"start_time": 12200,
								"end_time": 12700,
								"attribute": {
									"event": "speech"
								}
							}
						]
					}
				],
				// 弹窗相关状态
				showEditModal: false,
				currentEditIndex: -1,
				currentEditWords: []
			}
		},
		methods: {
			// 打开编辑弹窗
			openEditModal(index) {
				if (index < 0 || index >= this.utterances.length) {
					uni.showToast({
						title: '无效的索引',
						icon: 'error'
					});
					return;
				}

				this.currentEditIndex = index;
				this.currentEditWords = JSON.parse(JSON.stringify(this.utterances[index].words));
				this.showEditModal = true;
			},

			// 关闭编辑弹窗
			closeEditModal() {
				this.showEditModal = false;
				this.currentEditIndex = -1;
				this.currentEditWords = [];
			},

			// 更新单个字符
			updateWord(wordIndex, event) {
				const newText = event.detail.value;

				// 限制输入长度为1个字符
				if (newText.length <= 1) {
					this.currentEditWords[wordIndex].text = newText;
				} else {
					// 如果输入超过1个字符，只保留第一个字符
					this.currentEditWords[wordIndex].text = newText.charAt(0);

					// 更新输入框显示
					this.$nextTick(() => {
						const inputs = document.querySelectorAll('.word-input');
						if (inputs[wordIndex]) {
							inputs[wordIndex].value = newText.charAt(0);
						}
					});
				}
			},

			// 确认修改
			confirmEdit() {
				if (this.currentEditIndex >= 0) {
					try {
						// 验证数据完整性
						if (!this.currentEditWords || this.currentEditWords.length === 0) {
							uni.showToast({
								title: '没有可修改的数据',
								icon: 'error'
							});
							return;
						}

						// 更新 words 数组
						this.utterances[this.currentEditIndex].words = JSON.parse(JSON.stringify(this.currentEditWords));

						// 重新拼接 text 字段
						const newText = this.currentEditWords.map(word => word.text).join('');
						this.utterances[this.currentEditIndex].text = newText;

						uni.showToast({
							title: '修改成功',
							icon: 'success'
						});

						this.closeEditModal();
					} catch (error) {
						console.error('修改失败：', error);
						uni.showToast({
							title: '修改失败',
							icon: 'error'
						});
					}
				}
			},

			// 下一步操作
			handleNext() {
				try {
					// 验证数据完整性
					if (!this.utterances || this.utterances.length === 0) {
						uni.showToast({
							title: '没有可输出的数据',
							icon: 'error'
						});
						return;
					}

					// 输出完整数据
					const outputData = {
						utterances: this.utterances,
						timestamp: new Date().toISOString(),
						totalCount: this.utterances.length
					};

					console.log('修改后的完整数据：', outputData);

					// 可以在这里添加数据上传或保存逻辑
					// this.uploadData(outputData);

					uni.showModal({
						title: '数据输出成功',
						content: `已输出 ${this.utterances.length} 条语音识别结果到控制台`,
						showCancel: false,
						confirmText: '确定'
					});

				} catch (error) {
					console.error('输出数据失败：', error);
					uni.showToast({
						title: '输出失败',
						icon: 'error'
					});
				}
			},

			// 重置数据（可选功能）
			resetData() {
				uni.showModal({
					title: '确认重置',
					content: '是否要重置所有修改？',
					success: (res) => {
						if (res.confirm) {
							// 这里可以重新加载原始数据
							this.loadInitialData();
							uni.showToast({
								title: '已重置',
								icon: 'success'
							});
						}
					}
				});
			},

			// 加载初始数据
			loadInitialData() {
				// 这里可以从服务端重新获取数据
				// 目前使用示例数据
				console.log('重新加载数据');
			}
		},
		onLoad() {
			// 如果有存储的数据，可以在这里加载
			// this.videoList = uni.getStorageSync('clipData')
			console.log('页面加载完成，当前数据：', this.utterances);
		}
	}
</script>

<style lang="scss" scoped>
	page {
		width: 100%;
		overflow-x: hidden;
		background-color: #000;
	}

	.main {
		min-height: 100vh;
		background-color: #000;
		color: #fff;
		display: flex;
		flex-direction: column;
	}

	/* 页面标题 */
	.header {
		padding: 40rpx 30rpx 30rpx;
		border-bottom: 1px solid #333;

		.title {
			font-size: 36rpx;
			font-weight: bold;
			color: #fff;
		}
	}

	/* 内容区域 */
	.c-content {
		flex: 1;
		padding: 30rpx;
	}

	/* 语音识别结果列表 */
	.utterance-list {
		.utterance-item {
			margin-bottom: 30rpx;
			background-color: #111;
			border-radius: 12rpx;
			border: 1px solid #333;
			overflow: hidden;

			.utterance-content {
				padding: 30rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.utterance-text {
					flex: 1;
					font-size: 32rpx;
					line-height: 1.6;
					color: #fff;
					margin-right: 20rpx;
					word-break: break-all;
				}

				.edit-btn {
					background-color: #007AFF;
					color: #fff;
					border: none;
					border-radius: 8rpx;
					font-size: 28rpx;
					min-width: 120rpx;

					&:active {
						background-color: #0056CC;
					}
				}
			}
		}
	}

	/* 底部操作区域 */
	.footer {
		padding: 30rpx;
		border-top: 1px solid #333;

		.footer-buttons {
			display: flex;
			gap: 20rpx;

			.reset-btn {
				flex: 1;
				background-color: #FF3B30;
				color: #fff;
				border: none;
				border-radius: 12rpx;
				font-size: 32rpx;
				font-weight: bold;

				&:active {
					background-color: #D70015;
				}
			}

			.next-btn {
				flex: 2;
				background-color: #34C759;
				color: #fff;
				border: none;
				border-radius: 12rpx;
				font-size: 32rpx;
				font-weight: bold;

				&:active {
					background-color: #28A745;
				}
			}
		}
	}

	/* 弹窗遮罩 */
	.modal-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.7);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
		padding: 60rpx;
	}

	/* 弹窗内容 */
	.modal-content {
		background-color: #111;
		border-radius: 16rpx;
		border: 1px solid #333;
		width: 100%;
		max-width: 600rpx;
		max-height: 80vh;
		display: flex;
		flex-direction: column;
	}

	/* 弹窗头部 */
	.modal-header {
		padding: 30rpx;
		border-bottom: 1px solid #333;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.modal-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #fff;
		}

		.close-btn {
			font-size: 48rpx;
			color: #999;
			width: 60rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			&:active {
				color: #fff;
			}
		}
	}

	/* 弹窗主体 */
	.modal-body {
		flex: 1;
		padding: 30rpx;
		overflow-y: auto;

		.words-grid {
			display: grid;
			grid-template-columns: repeat(auto-fill, minmax(80rpx, 1fr));
			gap: 20rpx;

			.word-item {
				.word-input {
					width: 100%;
					height: 80rpx;
					background-color: #222;
					border: 2rpx solid #444;
					border-radius: 8rpx;
					color: #fff;
					text-align: center;
					font-size: 28rpx;

					&:focus {
						border-color: #007AFF;
						background-color: #333;
					}
				}
			}
		}
	}

	/* 弹窗底部 */
	.modal-footer {
		padding: 30rpx;
		border-top: 1px solid #333;
		display: flex;
		gap: 20rpx;

		.confirm-btn {
			flex: 1;
			background-color: #007AFF;
			color: #fff;
			border: none;
			border-radius: 8rpx;
			padding: 24rpx;
			font-size: 30rpx;

			&:active {
				background-color: #0056CC;
			}
		}

		.cancel-btn {
			flex: 1;
			background-color: #666;
			color: #fff;
			border: none;
			border-radius: 8rpx;
			padding: 24rpx;
			font-size: 30rpx;

			&:active {
				background-color: #555;
			}
		}
	}
</style>