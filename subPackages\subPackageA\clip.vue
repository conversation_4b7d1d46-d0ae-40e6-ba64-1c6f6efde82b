<template>
	<view class="main">

	</view>
</template>

<script>
	export default {
		data() {
			return {
				videoList: [],
				utterances: [
    {
      "text": "苏州的家人们看过来",
      "start_time": 2040,
      "end_time": 4220,
      "words": [
        {
          "text": "苏",
          "start_time": 2040,
          "end_time": 2200,
          "attribute": {
            "event": "speech"
          }
        },
        {
          "text": "州",
          "start_time": 2200,
          "end_time": 2360,
          "attribute": {
            "event": "speech"
          }
        },
        {
          "text": "的",
          "start_time": 2360,
          "end_time": 2560,
          "attribute": {
            "event": "speech"
          }
        },
        {
          "text": "家",
          "start_time": 2640,
          "end_time": 2880,
          "attribute": {
            "event": "speech"
          }
        },
        {
          "text": "人",
          "start_time": 2880,
          "end_time": 3000,
          "attribute": {
            "event": "speech"
          }
        },
        {
          "text": "们",
          "start_time": 3000,
          "end_time": 3200,
          "attribute": {
            "event": "speech"
          }
        },
        {
          "text": "看",
          "start_time": 3280,
          "end_time": 3520,
          "attribute": {
            "event": "speech"
          }
        },
        {
          "text": "过",
          "start_time": 3520,
          "end_time": 3720,
          "attribute": {
            "event": "speech"
          }
        },
        {
          "text": "来",
          "start_time": 3720,
          "end_time": 4220,
          "attribute": {
            "event": "speech"
          }
        }
      ]
    },
    {
      "text": "我们扎根在一个相城区",
      "start_time": 4320,
      "end_time": 7040,
      "words": [
        {
          "text": "我",
          "start_time": 4320,
          "end_time": 4680,
          "attribute": {
            "event": "speech"
          }
        },
        {
          "text": "们",
          "start_time": 4680,
          "end_time": 4960,
          "attribute": {
            "event": "speech"
          }
        },
        {
          "text": "扎",
          "start_time": 5000,
          "end_time": 5240,
          "attribute": {
            "event": "speech"
          }
        },
        {
          "text": "根",
          "start_time": 5280,
          "end_time": 5480,
          "attribute": {
            "event": "speech"
          }
        },
        {
          "text": "在",
          "start_time": 5480,
          "end_time": 5720,
          "attribute": {
            "event": "speech"
          }
        },
        {
          "text": "一",
          "start_time": 5960,
          "end_time": 6160,
          "attribute": {
            "event": "speech"
          }
        },
        {
          "text": "个",
          "start_time": 6160,
          "end_time": 6400,
          "attribute": {
            "event": "speech"
          }
        },
        {
          "text": "相",
          "start_time": 6400,
          "end_time": 6640,
          "attribute": {
            "event": "speech"
          }
        },
        {
          "text": "城",
          "start_time": 6640,
          "end_time": 6840,
          "attribute": {
            "event": "speech"
          }
        },
        {
          "text": "区",
          "start_time": 6840,
          "end_time": 7040,
          "attribute": {
            "event": "speech"
          }
        }
      ]
    },
    {
      "text": "八千平米的呃",
      "start_time": 7040,
      "end_time": 8920,
      "words": [
        {
          "text": "八",
          "start_time": 7040,
          "end_time": 7240,
          "attribute": {
            "event": "speech"
          }
        },
        {
          "text": "千",
          "start_time": 7240,
          "end_time": 7480,
          "attribute": {
            "event": "speech"
          }
        },
        {
          "text": "平",
          "start_time": 7480,
          "end_time": 7720,
          "attribute": {
            "event": "speech"
          }
        },
        {
          "text": "米",
          "start_time": 7720,
          "end_time": 7840,
          "attribute": {
            "event": "speech"
          }
        },
        {
          "text": "的",
          "start_time": 7840,
          "end_time": 8080,
          "attribute": {
            "event": "speech"
          }
        },
        {
          "text": "呃",
          "start_time": 8760,
          "end_time": 8920,
          "attribute": {
            "event": "speech"
          }
        }
      ]
    },
    {
      "text": "工厂直营店",
      "start_time": 8920,
      "end_time": 10220,
      "words": [
        {
          "text": "工",
          "start_time": 8920,
          "end_time": 9080,
          "attribute": {
            "event": "speech"
          }
        },
        {
          "text": "厂",
          "start_time": 9080,
          "end_time": 9320,
          "attribute": {
            "event": "speech"
          }
        },
        {
          "text": "直",
          "start_time": 9320,
          "end_time": 9520,
          "attribute": {
            "event": "speech"
          }
        },
        {
          "text": "营",
          "start_time": 9520,
          "end_time": 9680,
          "attribute": {
            "event": "speech"
          }
        },
        {
          "text": "店",
          "start_time": 9720,
          "end_time": 10220,
          "attribute": {
            "event": "speech"
          }
        }
      ]
    },
    {
      "text": "位置偏了点",
      "start_time": 11360,
      "end_time": 12700,
      "words": [
        {
          "text": "位",
          "start_time": 11360,
          "end_time": 11600,
          "attribute": {
            "event": "speech"
          }
        },
        {
          "text": "置",
          "start_time": 11600,
          "end_time": 11800,
          "attribute": {
            "event": "speech"
          }
        },
        {
          "text": "偏",
          "start_time": 11840,
          "end_time": 12040,
          "attribute": {
            "event": "speech"
          }
        },
        {
          "text": "了",
          "start_time": 12040,
          "end_time": 12200,
          "attribute": {
            "event": "speech"
          }
        },
        {
          "text": "点",
          "start_time": 12200,
          "end_time": 12700,
          "attribute": {
            "event": "speech"
          }
        }
      ]
    }
  ]
			}
		},
		methods: {
			/**
			 * 将JSON格式的语音数据转换为普通对象格式
			 * @param {Array} jsonData - 原始JSON格式的utterances数组
			 * @returns {Object} 转换后的普通对象格式
			 */
			parseUtterancesToObject(jsonData) {
				try {
					if (!Array.isArray(jsonData)) {
						console.error('输入数据必须是数组格式');
						return null;
					}

					const result = {
						// 句子级别的数据
						sentences: [],
						// 单词级别的数据（扁平化）
						words: [],
						// 时间轴数据（便于快速查询）
						timeline: [],
						// 统计信息
						stats: {
							totalSentences: 0,
							totalWords: 0,
							totalDuration: 0,
							startTime: 0,
							endTime: 0
						}
					};

					let wordIndex = 0;
					let minStartTime = Infinity;
					let maxEndTime = 0;

					jsonData.forEach((utterance, sentenceIndex) => {
						// 处理句子数据
						const sentence = {
							id: sentenceIndex,
							text: utterance.text,
							startTime: utterance.start_time,
							endTime: utterance.end_time,
							duration: utterance.end_time - utterance.start_time,
							wordCount: utterance.words ? utterance.words.length : 0,
							wordStartIndex: wordIndex,
							wordEndIndex: wordIndex + (utterance.words ? utterance.words.length - 1 : -1)
						};

						result.sentences.push(sentence);

						// 更新时间范围
						minStartTime = Math.min(minStartTime, utterance.start_time);
						maxEndTime = Math.max(maxEndTime, utterance.end_time);

						// 处理单词数据
						if (utterance.words && Array.isArray(utterance.words)) {
							utterance.words.forEach((word, wordIndexInSentence) => {
								const wordObj = {
									id: wordIndex,
									text: word.text,
									startTime: word.start_time,
									endTime: word.end_time,
									duration: word.end_time - word.start_time,
									sentenceId: sentenceIndex,
									wordIndexInSentence: wordIndexInSentence,
									attribute: word.attribute || { event: "speech" }
								};

								result.words.push(wordObj);

								// 添加到时间轴
								result.timeline.push({
									time: word.start_time,
									type: 'word_start',
									wordId: wordIndex,
									sentenceId: sentenceIndex,
									text: word.text
								});

								result.timeline.push({
									time: word.end_time,
									type: 'word_end',
									wordId: wordIndex,
									sentenceId: sentenceIndex,
									text: word.text
								});

								wordIndex++;
							});
						}

						// 添加句子时间点到时间轴
						result.timeline.push({
							time: utterance.start_time,
							type: 'sentence_start',
							sentenceId: sentenceIndex,
							text: utterance.text
						});

						result.timeline.push({
							time: utterance.end_time,
							type: 'sentence_end',
							sentenceId: sentenceIndex,
							text: utterance.text
						});
					});

					// 排序时间轴
					result.timeline.sort((a, b) => a.time - b.time);

					// 更新统计信息
					result.stats = {
						totalSentences: result.sentences.length,
						totalWords: result.words.length,
						totalDuration: maxEndTime - minStartTime,
						startTime: minStartTime === Infinity ? 0 : minStartTime,
						endTime: maxEndTime
					};

					console.log('语音数据转换为普通对象成功:', result);
					return result;

				} catch (error) {
					console.error('转换JSON到普通对象失败:', error);
					return null;
				}
			},

			/**
			 * 将普通对象格式转换回JSON格式的语音数据
			 * @param {Object} objectData - 普通对象格式的数据
			 * @returns {Array} 转换后的JSON格式utterances数组
			 */
			objectToUtterances(objectData) {
				try {
					if (!objectData || !objectData.sentences || !objectData.words) {
						console.error('输入的对象数据格式不正确');
						return null;
					}

					const result = [];

					objectData.sentences.forEach(sentence => {
						const utterance = {
							text: sentence.text,
							start_time: sentence.startTime,
							end_time: sentence.endTime,
							words: []
						};

						// 根据句子ID查找对应的单词
						const sentenceWords = objectData.words.filter(word => word.sentenceId === sentence.id);

						// 按照在句子中的位置排序
						sentenceWords.sort((a, b) => a.wordIndexInSentence - b.wordIndexInSentence);

						sentenceWords.forEach(word => {
							utterance.words.push({
								text: word.text,
								start_time: word.startTime,
								end_time: word.endTime,
								attribute: word.attribute || { event: "speech" }
							});
						});

						result.push(utterance);
					});

					console.log('普通对象转换为JSON格式成功:', result);
					return result;

				} catch (error) {
					console.error('转换普通对象到JSON失败:', error);
					return null;
				}
			},
		},
		onLoad() {
			this.videoList = uni.getStorageSync('clipData')
			console.log(this.videoList);

			l
		}
	}
</script>

<style lang="scss">
	page {
		width: 100%;
		overflow-x: hidden;
		background-color: #000;
		border-top: 1px solid rgb(56, 56, 56);
	}
</style>