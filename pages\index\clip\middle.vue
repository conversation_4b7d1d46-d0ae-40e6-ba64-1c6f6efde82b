<template>
	<view>
		
		<view class="bg" :style="{'background-image': 'url('+imgUrl+'405.png'+')'}">
		
			<view
				:style="{'height':''+heightSystemss+'px','top':''+statusBarHeightss+'px','lineHeight':''+heightSystemss+'px','left':''+10+'px'}"
				class="iconDizhssi">
				<image @click="navig()" class="img-34" :src="imgUrl + '34.png'"></image>
				<view class="font-size_30rpx" @click="navig()">
					视频合成
				</view>
			</view>
		
			<block v-for="(item,index) in banner" :key="index">
				<image @click="getUrl(item.url)" class="img-banner" mode="widthFix" :src="item.pic_url"></image>
			</block>
		
		</view>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
				imgUrl: this.$imgUrl,
				
				heightSystemss: '',
				statusBarHeightss: '',
				
				banner: [],
				
				cloneSet: uni.getStorageSync("cloneSet"), //克隆开关设置
				
			}
		},
		
		onLoad() {
			this.getSystemInfo();
			this.getBanner();
		},
		
		onShow() {
			
		},
		
		methods: {
			
			getUrl(url) {
				
				if (url == '/pages/index/clip/batchClip') {
					if (this.cloneSet.xunfei_sound_clone_swich == 1 || this.cloneSet.voice_high_open == 1) {
						uni.redirectTo({
							url: url
						})
					}else {
						this.$sun.toast("请先开启专业版声音或高保真声音");
					}
				}else {
					uni.redirectTo({
						url: url
					})
				}
			},
			
			//轮播图
			async getBanner() {
				const result = await this.$http.post({
					url: this.$api.banner,
					data: {
						b_type: 5
					}
				});
				if (result.errno == 0) {
					this.banner = result.data;
				}
			},
			
			navig() {
				let pages = getCurrentPages(); //获取所有页面栈实例列表
			
				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				} else {
					uni.navigateBack();
				}
			},
			
			getSystemInfo() {
				let menuButtonObject = uni.getMenuButtonBoundingClientRect(); //获取菜单按钮（右上角胶囊按钮）的布局位置信息。坐标信息以屏幕左上角为原点。
				uni.getSystemInfo({ //获取系统信息
					success: res => {
						let navHeight = menuButtonObject.height + (menuButtonObject.top - res
							.statusBarHeight) * 2; //导航栏高度=菜单按钮高度+（菜单按钮与顶部距离-状态栏高度）*2
						this.heightSystemss = navHeight;
						this.statusBarHeightss = res.statusBarHeight;
					},
					fail(err) {
						// console.log(err);
					}
				})
			},
			
		}
	}
</script>

<style lang="scss">
	
	.img-banner {
		width: 710rpx;
		margin-bottom: 24rpx;
	}
	
	.bg {
		width: 750rpx;
		height: 640rpx;
		background-repeat: no-repeat;
		background-size: cover;
		padding: 220rpx 20rpx 30rpx;
	}
	
	.img-34 {
		width: 40rpx;
		height: 40rpx;
	}
	
	.iconDizhssi {
		position: absolute;
		z-index: 999;
		color: #FFFFFF;
		display: flex;
		align-items: center;
	}
	
	page {
		border: none;
		background-color: #111317;
		width: 100%;
		overflow-x: hidden !important;
	}
	
</style>
